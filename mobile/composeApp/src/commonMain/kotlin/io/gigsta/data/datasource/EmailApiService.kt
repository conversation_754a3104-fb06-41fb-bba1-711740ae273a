package io.gigsta.data.datasource

import io.gigsta.data.model.EmailApplicationResponse
import io.gigsta.data.network.NetworkClient
import io.gigsta.data.network.NetworkConfig
import io.gigsta.domain.model.ResumeInfo
import io.gigsta.domain.model.JobInfo
import io.ktor.client.call.*
import io.ktor.client.request.forms.*
import io.ktor.http.*
import io.ktor.utils.io.core.*

class EmailApiService {
    
    private val httpClient = NetworkClient.client
    
    suspend fun generateEmailApplication(
        resumeInfo: ResumeInfo,
        jobInfo: JobInfo
    ): Result<EmailApplicationResponse> {
        return try {
            val formDataList = formData {
                // Add job description if provided
                jobInfo.description?.let { description ->
                    append("jobDescription", description)
                }

                // Add job image if provided
                jobInfo.imageData?.let { imageData ->
                    append(
                        key = "jobImage",
                        value = imageData,
                        headers = Headers.build {
                            append(HttpHeaders.ContentType, "image/jpeg")
                            append(HttpHeaders.ContentDisposition, "form-data; name=\"jobImage\"; filename=\"job_image.jpg\"")
                        }
                    )
                }

                // For unauthenticated resume (when fileData is available)
                resumeInfo.fileData?.let { fileData ->
                    val fileName = resumeInfo.fileName ?: "resume.pdf"
                    val mimeType = getMimeTypeFromFileName(fileName)

//                    append(
//                        key = "unauthenticatedResumeFile",
//                        value = fileData,
//                        headers = Headers.build {
//                            append(HttpHeaders.ContentType, mimeType)
//                            append(HttpHeaders.ContentDisposition, "form-data; name=\"unauthenticatedResumeFile\"; filename=\"$fileName\"")
//                        }
//                    )
//
//                    append("unauthenticatedResumeFileName", fileName)
                }
            }

            val response = httpClient.submitFormWithBinaryData(
                url = "${NetworkConfig.API_BASE_URL}/api/generate-email-application",
                formData = formDataList
            )
            
            if (response.status.isSuccess()) {
                val emailResponse = response.body<EmailApplicationResponse>()
                Result.success(emailResponse)
            } else {
                val errorBody = response.body<String>()
                Result.failure(Exception("API Error: ${response.status.value} - $errorBody"))
            }
            
        } catch (e: Exception) {
            println("Error generating email application: ${e.message}")
            Result.failure(Exception("Failed to generate email application: ${e.message}"))
        }
    }
    
    private fun getMimeTypeFromFileName(fileName: String?): String {
        return when (fileName?.lowercase()?.substringAfterLast('.')) {
            "pdf" -> "application/pdf"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "doc" -> "application/msword"
            "txt" -> "text/plain"
            "png" -> "image/png"
            "jpg", "jpeg" -> "image/jpeg"
            else -> "application/octet-stream"
        }
    }
}
